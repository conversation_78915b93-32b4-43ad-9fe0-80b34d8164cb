package com.xiang.proxy.server.core;

import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 统一的代理请求模型
 * 所有inbound组件都将请求封装成此格式
 */
public class ProxyRequest {
    private final String requestId;
    private final String protocol;
    private final String targetHost;
    private final int targetPort;
    private final ByteBuf data;
    private final Channel clientChannel;
    private final Map<String, Object> attributes;
    private final long timestamp;
    private final String clientId;
    private final int sessionId;

    private ProxyRequest(Builder builder) {
        this.requestId = builder.requestId != null ? builder.requestId : UUID.randomUUID().toString();
        this.protocol = builder.protocol;
        this.targetHost = builder.targetHost;
        this.targetPort = builder.targetPort;
        this.data = builder.data;
        this.clientChannel = builder.clientChannel;
        this.attributes = new HashMap<>(builder.attributes);
        this.timestamp = builder.timestamp > 0 ? builder.timestamp : System.currentTimeMillis();
        this.clientId = builder.clientId;
        this.sessionId = builder.sessionId;
    }

    // Getters
    public String getRequestId() { return requestId; }
    public String getProtocol() { return protocol; }
    public String getTargetHost() { return targetHost; }
    public int getTargetPort() { return targetPort; }
    public ByteBuf getData() { return data; }
    public Channel getClientChannel() { return clientChannel; }
    public Map<String, Object> getAttributes() { return attributes; }
    public long getTimestamp() { return timestamp; }
    public String getClientId() { return clientId; }
    public int getSessionId() { return sessionId; }

    // 便捷方法
    public String getTarget() {
        return targetHost + ":" + targetPort;
    }

    public boolean hasData() {
        return data != null && data.isReadable();
    }

    public String getClientAddress() {
        return clientChannel != null ? clientChannel.remoteAddress().toString() : "unknown";
    }

    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    public <T> T getAttribute(String key, T defaultValue) {
        T value = getAttribute(key);
        return value != null ? value : defaultValue;
    }

    @Override
    public String toString() {
        return String.format("ProxyRequest{id=%s, protocol=%s, target=%s:%d, client=%s, session=%d}",
                requestId, protocol, targetHost, targetPort, getClientAddress(), sessionId);
    }

    // Builder模式
    public static class Builder {
        private String requestId;
        private String protocol;
        private String targetHost;
        private int targetPort;
        private ByteBuf data;
        private Channel clientChannel;
        private Map<String, Object> attributes = new HashMap<>();
        private long timestamp;
        private String clientId;
        private int sessionId;

        public Builder requestId(String requestId) {
            this.requestId = requestId;
            return this;
        }

        public Builder protocol(String protocol) {
            this.protocol = protocol;
            return this;
        }

        public Builder target(String host, int port) {
            this.targetHost = host;
            this.targetPort = port;
            return this;
        }

        public Builder data(ByteBuf data) {
            this.data = data;
            return this;
        }

        public Builder clientChannel(Channel channel) {
            this.clientChannel = channel;
            return this;
        }

        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }

        public Builder attributes(Map<String, Object> attributes) {
            this.attributes.putAll(attributes);
            return this;
        }

        public Builder timestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Builder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public Builder sessionId(int sessionId) {
            this.sessionId = sessionId;
            return this;
        }

        public ProxyRequest build() {
            if (protocol == null) {
                throw new IllegalArgumentException("Protocol is required");
            }

            //data package没有host和 port
//            if (targetHost == null || targetHost.trim().isEmpty()) {
//                throw new IllegalArgumentException("Target host is required");
//            }
//            if (targetPort <= 0 || targetPort > 65535) {
//                throw new IllegalArgumentException("Invalid target port: " + targetPort);
//            }
            if (clientChannel == null) {
                throw new IllegalArgumentException("Client channel is required");
            }

            return new ProxyRequest(this);
        }
    }

    // 静态工厂方法
    public static Builder builder() {
        return new Builder();
    }

    // 协议常量
    public static final class Protocol {
        public static final String TCP = "TCP";
        public static final String UDP = "UDP";
        public static final String HTTP = "HTTP";
        public static final String HTTPS = "HTTPS";
        public static final String SOCKS5 = "SOCKS5";
        public static final String MULTIPLEX = "MULTIPLEX";
    }

    // 属性键常量
    public static final class Attributes {
        public static final String AUTH_USER = "auth.user";
        public static final String CLIENT_IP = "client.ip";
        public static final String CONNECTION_ID = "connection.id";
        public static final String ORIGINAL_PROTOCOL = "original.protocol";
        public static final String ROUTE_RULE_ID = "route.rule.id";
        public static final String OUTBOUND_ID = "outbound.id";
    }
}