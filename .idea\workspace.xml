<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="49980bdc-5d33-4caf-ac98-620ab2f8b564" name="Changes" comment="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/ProxyProcessor.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/ProxyProcessor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/ProxyRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/ProxyRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexSession.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexSession.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/OutboundConnection.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/OutboundConnection.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/impl/TcpDirectOutboundHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/impl/TcpDirectOutboundHandler.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="302n8CxA6tIiSghQGFsbl4Zs7X5" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.HighPerformanceProxyServer.executor&quot;: &quot;Debug&quot;,
    &quot;Application.OptimizedProxyServer.executor&quot;: &quot;Debug&quot;,
    &quot;Application.ProxyClient (1).executor&quot;: &quot;Debug&quot;,
    &quot;Application.ProxyClient.executor&quot;: &quot;Debug&quot;,
    &quot;Application.ProxyServerV2 (1).executor&quot;: &quot;Debug&quot;,
    &quot;Application.ProxyServerV2.executor&quot;: &quot;Debug&quot;,
    &quot;Application.SimpleHighPerformanceProxyServer.executor&quot;: &quot;Debug&quot;,
    &quot;Application.com.proxy.server.ProxyServerV2.executor&quot;: &quot;Debug&quot;,
    &quot;Maven.netty-multiplex-proxy-alicloud-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.netty-multiplex-proxy-alicloud-parent [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.netty-multiplex-proxy-alicloud-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.netty-multiplex-proxy-alicloud-parent [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.netty-multiplex-proxy-service [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.netty-multiplex-proxy-service [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.netty-multiplex-service [clean...].executor&quot;: &quot;Debug&quot;,
    &quot;Maven.netty-multiplex-service [compile...].executor&quot;: &quot;Debug&quot;,
    &quot;Maven.netty_multiplex_proxy_alicloud [compile...].executor&quot;: &quot;Debug&quot;,
    &quot;Maven.proxy-server [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.proxy-server [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.proxy-server [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.springcloud-alibaba-chat-service [compile...].executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Spring Boot.Bootstrap.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;onboarding.tips.debug.path&quot;: &quot;C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy_alicloud/netty-multiplex-service/src/main/java/com/xiang/Main.java&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud\netty-multiplex-proxy-service\src\main\java\com\xiang\proxy\server\core" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud\configs\mysql\init" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud\configs" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud\netty-multiplex-proxy-service" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud\netty-multiplex-proxy-service" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.xiang.proxy.server" />
      <recent name="com.xiang.proxy.server.config" />
    </key>
  </component>
  <component name="RunManager" selected="Application.ProxyServerV2 (1)">
    <configuration name="HighPerformanceProxyServer" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xiang.proxy.server.HighPerformanceProxyServer" />
      <module name="proxy-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.proxy.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OptimizedProxyServer" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xiang.proxy.server.OptimizedProxyServer" />
      <module name="proxy-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.proxy.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProxyClient" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.proxy.client.ProxyClient" />
      <module name="proxy-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.proxy.client.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProxyServerV2 (1)" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xiang.proxy.server.ProxyServerV2" />
      <module name="proxy-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.proxy.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SimpleHighPerformanceProxyServer" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xiang.proxy.server.SimpleHighPerformanceProxyServer" />
      <module name="proxy-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.proxy.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Bootstrap" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="netty-multiplex-proxy-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xiang.proxy.server.Bootstrap" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ProxyServerV2 (1)" />
        <item itemvalue="Application.ProxyClient" />
        <item itemvalue="Application.SimpleHighPerformanceProxyServer" />
        <item itemvalue="Application.HighPerformanceProxyServer" />
        <item itemvalue="Application.OptimizedProxyServer" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="49980bdc-5d33-4caf-ac98-620ab2f8b564" name="Changes" comment="" />
      <created>1752834374165</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752834374165</updated>
      <workItem from="1752834374958" duration="287000" />
      <workItem from="1755675237535" duration="36526000" />
      <workItem from="1755714995870" duration="42000" />
      <workItem from="1755741117825" duration="53584000" />
      <workItem from="1755796014671" duration="1377000" />
      <workItem from="1755820190688" duration="4237000" />
    </task>
    <task id="LOCAL-00001" summary="代理服务器导入，netty多路复用代理">
      <option name="closed" value="true" />
      <created>1752834469506</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752834469507</updated>
    </task>
    <task id="LOCAL-00002" summary="移除target">
      <option name="closed" value="true" />
      <created>1752834614918</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752834614918</updated>
    </task>
    <task id="LOCAL-00003" summary="完成proxy-server与springcloud alibaba整合。">
      <option name="closed" value="true" />
      <created>1755686970386</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755686970386</updated>
    </task>
    <task id="LOCAL-00004" summary="完成proxy-server与springcloud alibaba整合。">
      <option name="closed" value="true" />
      <created>1755687634526</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755687634526</updated>
    </task>
    <task id="LOCAL-00005" summary="proxy-client实现接入nacos，支持前端负载均衡，从nacos查询可用节点。">
      <option name="closed" value="true" />
      <created>1755696336508</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755696336508</updated>
    </task>
    <task id="LOCAL-00006" summary="proxy-client实现接入nacos，支持前端负载均衡，从nacos查询可用节点。">
      <option name="closed" value="true" />
      <created>1755697762424</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755697762424</updated>
    </task>
    <task id="LOCAL-00007" summary="修复docker-compose部署，通过nacos负载均衡。">
      <option name="closed" value="true" />
      <created>1755715018416</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755715018417</updated>
    </task>
    <task id="LOCAL-00008" summary="proxy-server支持inbound与outbound通过队列解耦合，并改善吞吐量。">
      <option name="closed" value="true" />
      <created>1755746566047</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755746566047</updated>
    </task>
    <task id="LOCAL-00009" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755747147187</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755747147187</updated>
    </task>
    <task id="LOCAL-00010" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755747243198</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755747243198</updated>
    </task>
    <task id="LOCAL-00011" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755763425467</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1755763425467</updated>
    </task>
    <task id="LOCAL-00012" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755770082337</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1755770082337</updated>
    </task>
    <task id="LOCAL-00013" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755774354114</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1755774354114</updated>
    </task>
    <task id="LOCAL-00014" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755789394891</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1755789394891</updated>
    </task>
    <option name="localTasksCounter" value="15" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="2bd49786-28c7-4e17-85b5-7a1e42ae161c" value="TOOL_WINDOW" />
        <entry key="0b9717a2-e48a-498f-994e-8fec4d7762fc" value="TOOL_WINDOW" />
        <entry key="28d4ce46-29e5-41e3-b3b5-5361e8d98e5a" value="TOOL_WINDOW" />
        <entry key="a0bdf37f-ded9-4c82-b790-656720b361ec" value="TOOL_WINDOW" />
        <entry key="969bc060-62bb-47d8-9150-fb69eabb7a89" value="TOOL_WINDOW" />
        <entry key="3b158550-6eaf-4210-a517-0b73605ab9c7" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="0b9717a2-e48a-498f-994e-8fec4d7762fc">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="28d4ce46-29e5-41e3-b3b5-5361e8d98e5a">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="2bd49786-28c7-4e17-85b5-7a1e42ae161c">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="3b158550-6eaf-4210-a517-0b73605ab9c7">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="969bc060-62bb-47d8-9150-fb69eabb7a89">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="a0bdf37f-ded9-4c82-b790-656720b361ec">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="代理服务器导入，netty多路复用代理" />
    <MESSAGE value="移除target" />
    <MESSAGE value="完成proxy-server与springcloud alibaba整合。" />
    <MESSAGE value="proxy-client实现接入nacos，支持前端负载均衡，从nacos查询可用节点。" />
    <MESSAGE value="修复docker-compose部署，通过nacos负载均衡。" />
    <MESSAGE value="proxy-server支持inbound与outbound通过队列解耦合，并改善吞吐量。" />
    <MESSAGE value="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码" />
    <option name="LAST_COMMIT_MESSAGE" value="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码" />
  </component>
</project>